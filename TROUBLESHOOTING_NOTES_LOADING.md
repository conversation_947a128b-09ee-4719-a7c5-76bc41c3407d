# 筆記頁面載入問題診斷和解決方案

## 問題描述
筆記頁面（/notes）顯示持續的載入 Spinner，無法正常載入筆記列表和資料夾樹狀結構。

## 診斷步驟

### 1. 使用調試組件
我已經創建了一個調試組件 `NotesDebug.svelte` 來幫助診斷問題：

```bash
# 臨時修改筆記頁面使用調試組件
# 文件: src/routes/(app)/notes/+page.svelte
# 將 <NotesWithFolders /> 改為 <NotesDebug />
```

### 2. 運行診斷腳本
```bash
python debug_notes_loading.py
```

### 3. 檢查瀏覽器開發者工具
1. 打開瀏覽器開發者工具 (F12)
2. 查看 Console 標籤中的錯誤信息
3. 查看 Network 標籤中失敗的 API 請求

## 常見問題和解決方案

### 問題 1: 數據庫遷移未執行
**症狀**: API 返回數據庫錯誤或表不存在錯誤

**解決方案**:
```bash
cd backend
alembic upgrade head
```

**驗證**:
```bash
# 檢查數據庫表是否存在
sqlite3 backend/data/webui.db ".tables" | grep note_folder
```

### 問題 2: 後端路由未正確註冊
**症狀**: API 請求返回 404 錯誤

**檢查**:
1. 確認 `backend/open_webui/main.py` 中包含：
```python
from open_webui.routers import note_folders
app.include_router(note_folders.router, prefix="/api/v1/notes/folders", tags=["note_folders"])
```

2. 重啟後端服務：
```bash
cd backend
python -m open_webui.main
```

### 問題 3: 前端 API 調用錯誤
**症狀**: Network 標籤顯示 API 請求失敗

**檢查**:
1. 確認 API 基礎 URL 正確
2. 檢查認證 token 是否有效
3. 查看具體的錯誤響應

### 問題 4: 權限問題
**症狀**: API 返回 401 或 403 錯誤

**解決方案**:
1. 檢查用戶是否有筆記功能權限
2. 確認 token 未過期
3. 重新登錄獲取新 token

### 問題 5: 前端組件錯誤
**症狀**: Console 中有 JavaScript 錯誤

**檢查**:
1. 確認所有依賴文件都已創建
2. 檢查組件導入路徑
3. 查看具體的錯誤堆棧

## 修復後的組件改進

### 1. 改進的錯誤處理
```javascript
// NotesWithFolders.svelte 中的改進
let loading = false;
let error: string | null = null;

const init = async () => {
    loading = true;
    error = null;
    try {
        await loadNotes();
    } catch (err) {
        console.error('初始化失敗:', err);
        error = `初始化失敗: ${err}`;
    } finally {
        loading = false;
        loaded = true;
    }
};
```

### 2. 更好的載入狀態顯示
```svelte
{#if loading}
    <div class="flex items-center justify-center h-64">
        <Spinner />
    </div>
{:else if error}
    <div class="flex flex-col items-center justify-center h-64 text-center">
        <div class="text-red-500 dark:text-red-400 mb-4">
            {error}
        </div>
        <button on:click={init}>重試</button>
    </div>
{:else}
    <!-- 正常內容 -->
{/if}
```

### 3. 調試日誌
```javascript
// 添加詳細的調試日誌
console.log('正在載入資料夾樹...');
console.log('載入的筆記數量:', notesList?.length || 0);
```

## 逐步解決流程

### 步驟 1: 基礎檢查
1. 確認後端服務正在運行
2. 檢查數據庫文件是否存在
3. 驗證用戶已登錄且有有效 token

### 步驟 2: 數據庫檢查
```bash
# 檢查數據庫表
python debug_notes_loading.py

# 或手動檢查
sqlite3 backend/data/webui.db
.tables
.schema note_folder
.schema note
```

### 步驟 3: API 測試
```bash
# 測試基本筆記 API
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/api/v1/notes/

# 測試資料夾 API
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8080/api/v1/notes/folders/tree
```

### 步驟 4: 前端調試
1. 使用 `NotesDebug.svelte` 組件
2. 查看瀏覽器 Console 輸出
3. 檢查 Network 請求詳情

### 步驟 5: 逐步恢復
1. 確認所有 API 都正常工作
2. 將調試組件改回 `NotesWithFolders`
3. 測試完整功能

## 預防措施

### 1. 添加健康檢查端點
```python
# 在後端添加健康檢查
@router.get("/health")
async def health_check():
    return {"status": "ok", "features": ["notes", "folders"]}
```

### 2. 改進錯誤處理
- 在所有 API 調用中添加詳細的錯誤處理
- 提供用戶友好的錯誤信息
- 添加重試機制

### 3. 添加載入狀態
- 為所有異步操作添加載入指示器
- 提供進度反饋
- 避免無限載入狀態

## 常用調試命令

```bash
# 檢查後端日誌
tail -f backend/logs/app.log

# 檢查數據庫內容
sqlite3 backend/data/webui.db "SELECT * FROM note_folder LIMIT 5;"

# 重啟服務
cd backend && python -m open_webui.main

# 清除瀏覽器緩存
# 在開發者工具中右鍵刷新按鈕 -> "清空緩存並硬性重新載入"
```

## 聯繫支持

如果問題仍然存在，請提供以下信息：
1. 瀏覽器 Console 錯誤截圖
2. Network 標籤中失敗請求的詳情
3. 後端服務日誌
4. 診斷腳本的輸出結果

#!/usr/bin/env python3
"""
診斷筆記頁面載入問題的腳本
"""

import requests
import json
import sys
import sqlite3
import os
from pathlib import Path

# 配置
BASE_URL = "http://localhost:8080/api/v1"
DB_PATH = "backend/data/webui.db"  # 調整為實際的數據庫路徑

def check_database_tables():
    """檢查數據庫表是否正確創建"""
    print("🔍 檢查數據庫表...")
    
    if not os.path.exists(DB_PATH):
        print(f"❌ 數據庫文件不存在: {DB_PATH}")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 檢查 note_folder 表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='note_folder';")
        if cursor.fetchone():
            print("✅ note_folder 表存在")
            
            # 檢查表結構
            cursor.execute("PRAGMA table_info(note_folder);")
            columns = [row[1] for row in cursor.fetchall()]
            expected_columns = ['id', 'user_id', 'name', 'description', 'color', 'parent_id', 'sort_order', 'access_control', 'created_at', 'updated_at']
            
            missing_columns = [col for col in expected_columns if col not in columns]
            if missing_columns:
                print(f"❌ note_folder 表缺少字段: {missing_columns}")
            else:
                print("✅ note_folder 表結構正確")
        else:
            print("❌ note_folder 表不存在")
            return False
        
        # 檢查 note 表的 folder_id 字段
        cursor.execute("PRAGMA table_info(note);")
        columns = [row[1] for row in cursor.fetchall()]
        if 'folder_id' in columns:
            print("✅ note 表包含 folder_id 字段")
        else:
            print("❌ note 表缺少 folder_id 字段")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 數據庫檢查失敗: {e}")
        return False

def check_backend_routes():
    """檢查後端路由是否可訪問"""
    print("\n🔍 檢查後端路由...")
    
    routes_to_check = [
        "/notes/folders/",
        "/notes/folders/tree",
        "/notes/folders/root/notes"
    ]
    
    for route in routes_to_check:
        try:
            url = f"{BASE_URL}{route}"
            print(f"檢查路由: {url}")
            
            # 不帶認證的請求，應該返回 401 而不是 404
            response = requests.get(url, timeout=5)
            
            if response.status_code == 401:
                print(f"✅ 路由 {route} 存在（需要認證）")
            elif response.status_code == 404:
                print(f"❌ 路由 {route} 不存在")
            else:
                print(f"⚠️ 路由 {route} 返回狀態碼: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 無法連接到後端服務: {BASE_URL}")
            return False
        except Exception as e:
            print(f"❌ 檢查路由 {route} 時出錯: {e}")
    
    return True

def check_frontend_files():
    """檢查前端文件是否存在"""
    print("\n🔍 檢查前端文件...")
    
    files_to_check = [
        "src/lib/apis/note-folders/index.ts",
        "src/lib/components/notes/FolderTree.svelte",
        "src/lib/components/notes/NotesWithFolders.svelte"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def check_api_with_auth():
    """使用認證檢查 API（需要提供 token）"""
    print("\n🔍 檢查 API 認證...")
    
    # 這裡需要實際的 token，可以從瀏覽器開發者工具中獲取
    token = input("請輸入用戶 token（可選，按 Enter 跳過）: ").strip()
    
    if not token:
        print("⚠️ 跳過認證 API 測試")
        return True
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # 測試獲取資料夾樹
        response = requests.get(f"{BASE_URL}/notes/folders/tree", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 資料夾樹 API 正常，返回 {len(data)} 個根資料夾")
        elif response.status_code == 401:
            print("❌ Token 無效或已過期")
        else:
            print(f"❌ 資料夾樹 API 錯誤: {response.status_code} - {response.text}")
        
        # 測試獲取根目錄筆記
        response = requests.get(f"{BASE_URL}/notes/folders/root/notes", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 根目錄筆記 API 正常，返回 {len(data)} 個筆記")
        else:
            print(f"❌ 根目錄筆記 API 錯誤: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ API 測試失敗: {e}")
        return False
    
    return True

def check_migration_status():
    """檢查遷移狀態"""
    print("\n🔍 檢查數據庫遷移狀態...")
    
    try:
        # 檢查 alembic_version 表
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='alembic_version';")
        if cursor.fetchone():
            cursor.execute("SELECT version_num FROM alembic_version;")
            version = cursor.fetchone()
            if version:
                print(f"✅ 當前數據庫版本: {version[0]}")
            else:
                print("⚠️ 沒有遷移版本記錄")
        else:
            print("❌ alembic_version 表不存在，可能沒有運行遷移")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 檢查遷移狀態失敗: {e}")

def generate_debug_report():
    """生成調試報告"""
    print("\n📋 生成調試報告...")
    
    report = {
        "database_ok": False,
        "backend_routes_ok": False,
        "frontend_files_ok": False,
        "recommendations": []
    }
    
    # 檢查數據庫
    if check_database_tables():
        report["database_ok"] = True
    else:
        report["recommendations"].append("運行數據庫遷移: cd backend && alembic upgrade head")
    
    # 檢查遷移狀態
    check_migration_status()
    
    # 檢查後端路由
    if check_backend_routes():
        report["backend_routes_ok"] = True
    else:
        report["recommendations"].append("檢查後端服務是否正在運行")
        report["recommendations"].append("檢查 note_folders 路由是否正確註冊")
    
    # 檢查前端文件
    if check_frontend_files():
        report["frontend_files_ok"] = True
    else:
        report["recommendations"].append("確保所有前端文件都已正確創建")
    
    # API 認證測試
    check_api_with_auth()
    
    # 輸出報告
    print("\n📊 診斷報告:")
    print(f"數據庫狀態: {'✅' if report['database_ok'] else '❌'}")
    print(f"後端路由: {'✅' if report['backend_routes_ok'] else '❌'}")
    print(f"前端文件: {'✅' if report['frontend_files_ok'] else '❌'}")
    
    if report["recommendations"]:
        print("\n🔧 建議的解決步驟:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"{i}. {rec}")
    else:
        print("\n🎉 所有基本檢查都通過了！")
        print("如果頁面仍然無法載入，請檢查瀏覽器開發者工具的 Console 和 Network 標籤")

def main():
    """主函數"""
    print("🚀 開始診斷筆記頁面載入問題\n")
    
    # 檢查當前目錄
    if not os.path.exists("backend") and not os.path.exists("src"):
        print("❌ 請在 Open WebUI 項目根目錄下運行此腳本")
        sys.exit(1)
    
    generate_debug_report()
    
    print("\n💡 額外調試建議:")
    print("1. 打開瀏覽器開發者工具 (F12)")
    print("2. 查看 Console 標籤中的錯誤信息")
    print("3. 查看 Network 標籤中失敗的 API 請求")
    print("4. 檢查 localStorage 中是否有有效的 token")
    print("5. 嘗試直接訪問 API 端點測試")

if __name__ == "__main__":
    main()

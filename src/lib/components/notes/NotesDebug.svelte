<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { WEBUI_NAME } from '$lib/stores';
	
	import { getNotesInRoot, getNoteFolderTree } from '$lib/apis/note-folders';
	import Spinner from '../common/Spinner.svelte';

	const i18n = getContext('i18n');

	let loaded = false;
	let loading = false;
	let error: string | null = null;
	let debugInfo: any = {};

	// 測試各個 API 端點
	const testAPIs = async () => {
		loading = true;
		error = null;
		debugInfo = {};

		try {
			console.log('開始 API 測試...');
			
			// 檢查 localStorage token
			const token = localStorage.token;
			console.log('Token 存在:', !!token);
			debugInfo.hasToken = !!token;
			
			if (!token) {
				throw new Error('沒有找到認證 token');
			}

			// 測試資料夾樹 API
			console.log('測試資料夾樹 API...');
			try {
				const folderTree = await getNoteFolderTree(token);
				console.log('資料夾樹 API 成功:', folderTree);
				debugInfo.folderTreeSuccess = true;
				debugInfo.folderTreeData = folderTree;
			} catch (err) {
				console.error('資料夾樹 API 失敗:', err);
				debugInfo.folderTreeSuccess = false;
				debugInfo.folderTreeError = err.toString();
			}

			// 測試根目錄筆記 API
			console.log('測試根目錄筆記 API...');
			try {
				const rootNotes = await getNotesInRoot(token);
				console.log('根目錄筆記 API 成功:', rootNotes);
				debugInfo.rootNotesSuccess = true;
				debugInfo.rootNotesData = rootNotes;
			} catch (err) {
				console.error('根目錄筆記 API 失敗:', err);
				debugInfo.rootNotesSuccess = false;
				debugInfo.rootNotesError = err.toString();
			}

			// 測試基本筆記 API
			console.log('測試基本筆記 API...');
			try {
				const response = await fetch('/api/v1/notes/', {
					headers: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				});
				
				if (response.ok) {
					const notes = await response.json();
					console.log('基本筆記 API 成功:', notes);
					debugInfo.basicNotesSuccess = true;
					debugInfo.basicNotesData = notes;
				} else {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}
			} catch (err) {
				console.error('基本筆記 API 失敗:', err);
				debugInfo.basicNotesSuccess = false;
				debugInfo.basicNotesError = err.toString();
			}

		} catch (err) {
			console.error('API 測試失敗:', err);
			error = err.toString();
		} finally {
			loading = false;
			loaded = true;
		}
	};

	onMount(async () => {
		await testAPIs();
	});
</script>

<svelte:head>
	<title>
		筆記調試 • {$WEBUI_NAME}
	</title>
</svelte:head>

<div class="w-full h-full p-6">
	<h1 class="text-2xl font-bold mb-6">筆記功能調試頁面</h1>

	{#if loading}
		<div class="flex items-center justify-center h-64">
			<Spinner />
			<span class="ml-3">正在測試 API...</span>
		</div>
	{:else if error}
		<div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
			<h3 class="text-lg font-medium text-red-800 dark:text-red-200 mb-2">錯誤</h3>
			<p class="text-red-700 dark:text-red-300">{error}</p>
		</div>
	{:else if loaded}
		<div class="space-y-6">
			<!-- Token 檢查 -->
			<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
				<h3 class="text-lg font-medium mb-2">認證 Token</h3>
				<div class="flex items-center">
					{#if debugInfo.hasToken}
						<span class="text-green-600 dark:text-green-400">✅ Token 存在</span>
					{:else}
						<span class="text-red-600 dark:text-red-400">❌ Token 不存在</span>
					{/if}
				</div>
			</div>

			<!-- 資料夾樹 API -->
			<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
				<h3 class="text-lg font-medium mb-2">資料夾樹 API</h3>
				<div class="flex items-center mb-2">
					{#if debugInfo.folderTreeSuccess}
						<span class="text-green-600 dark:text-green-400">✅ 成功</span>
					{:else}
						<span class="text-red-600 dark:text-red-400">❌ 失敗</span>
					{/if}
				</div>
				{#if debugInfo.folderTreeError}
					<p class="text-red-600 dark:text-red-400 text-sm mb-2">錯誤: {debugInfo.folderTreeError}</p>
				{/if}
				{#if debugInfo.folderTreeData}
					<details class="text-sm">
						<summary class="cursor-pointer text-blue-600 dark:text-blue-400">查看數據</summary>
						<pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded overflow-auto">{JSON.stringify(debugInfo.folderTreeData, null, 2)}</pre>
					</details>
				{/if}
			</div>

			<!-- 根目錄筆記 API -->
			<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
				<h3 class="text-lg font-medium mb-2">根目錄筆記 API</h3>
				<div class="flex items-center mb-2">
					{#if debugInfo.rootNotesSuccess}
						<span class="text-green-600 dark:text-green-400">✅ 成功</span>
					{:else}
						<span class="text-red-600 dark:text-red-400">❌ 失敗</span>
					{/if}
				</div>
				{#if debugInfo.rootNotesError}
					<p class="text-red-600 dark:text-red-400 text-sm mb-2">錯誤: {debugInfo.rootNotesError}</p>
				{/if}
				{#if debugInfo.rootNotesData}
					<p class="text-sm mb-2">筆記數量: {debugInfo.rootNotesData.length}</p>
					<details class="text-sm">
						<summary class="cursor-pointer text-blue-600 dark:text-blue-400">查看數據</summary>
						<pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded overflow-auto">{JSON.stringify(debugInfo.rootNotesData, null, 2)}</pre>
					</details>
				{/if}
			</div>

			<!-- 基本筆記 API -->
			<div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
				<h3 class="text-lg font-medium mb-2">基本筆記 API</h3>
				<div class="flex items-center mb-2">
					{#if debugInfo.basicNotesSuccess}
						<span class="text-green-600 dark:text-green-400">✅ 成功</span>
					{:else}
						<span class="text-red-600 dark:text-red-400">❌ 失敗</span>
					{/if}
				</div>
				{#if debugInfo.basicNotesError}
					<p class="text-red-600 dark:text-red-400 text-sm mb-2">錯誤: {debugInfo.basicNotesError}</p>
				{/if}
				{#if debugInfo.basicNotesData}
					<p class="text-sm mb-2">筆記數量: {Array.isArray(debugInfo.basicNotesData) ? debugInfo.basicNotesData.length : '未知'}</p>
					<details class="text-sm">
						<summary class="cursor-pointer text-blue-600 dark:text-blue-400">查看數據</summary>
						<pre class="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded overflow-auto">{JSON.stringify(debugInfo.basicNotesData, null, 2)}</pre>
					</details>
				{/if}
			</div>

			<!-- 重新測試按鈕 -->
			<div class="flex justify-center">
				<button
					type="button"
					class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
					on:click={testAPIs}
				>
					重新測試
				</button>
			</div>
		</div>
	{/if}
</div>

<style>
	pre {
		max-height: 200px;
		font-size: 12px;
	}
</style>
